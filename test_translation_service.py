#!/usr/bin/env python3
"""
Test script for the new DirectTranslationService
This script tests the basic functionality without requiring the full Flask app
"""

import sys
import os
import pandas as pd
from openpyxl import Workbook

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

def create_test_excel():
    """Create a test Excel file for testing"""
    # Create a simple test Excel file
    data = {
        'Product Name': [
            '30kg産業用洗濯機',
            'ステンレス製乾燥機',
            '商業用アイロンシステム',
            '高温スチーム機能付き洗濯機',
            '自動折りたたみ機械'
        ],
        'Description': [
            '高効率の産業用洗濯機',
            'ステンレス鋼製の耐久性のある乾燥機',
            'プロ仕様のアイロンシステム',
            'スチーム機能付きの高性能洗濯機',
            '自動で衣類を折りたたむ機械'
        ],
        'Category': [
            '洗濯機',
            '乾燥機',
            'アイロン',
            '洗濯機',
            '折りたたみ機'
        ]
    }
    
    df = pd.DataFrame(data)
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join('src', 'agents', 'eproexcella_agent', 'upload')
    os.makedirs(upload_dir, exist_ok=True)
    
    # Save test file
    test_file_path = os.path.join(upload_dir, 'data_test_user.xlsx')
    df.to_excel(test_file_path, index=False)
    
    print(f"Created test Excel file: {test_file_path}")
    return test_file_path

def test_translation_service():
    """Test the DirectTranslationService functionality"""
    try:
        # Import the service (this might fail due to missing dependencies)
        from src.backend.blueprints.translator_bot.translation_service import DirectTranslationService
        
        # Create test Excel file
        test_file_path = create_test_excel()
        
        # Initialize service
        service = DirectTranslationService('test_user')
        
        # Test getting columns
        print("Testing get_excel_columns...")
        columns = service.get_excel_columns()
        print(f"Columns found: {columns}")
        
        # Test reading column to JSON
        print("\nTesting read_excel_column_to_json...")
        if columns:
            json_arrays, header = service.read_excel_column_to_json(columns[0], max_rows=3)
            print(f"Header: {header}")
            print(f"JSON arrays: {json_arrays}")
        
        # Test default prompt generation
        print("\nTesting get_default_prompt...")
        prompt = service.get_default_prompt('English')
        print(f"Generated prompt: {prompt[:100]}...")
        
        print("\n✅ Basic functionality tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("This is expected in the current environment due to missing dependencies.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_file_structure():
    """Test that the file structure is correct"""
    print("Testing file structure...")
    
    # Check if translation service file exists
    service_file = 'src/backend/blueprints/translator_bot/translation_service.py'
    if os.path.exists(service_file):
        print(f"✅ {service_file} exists")
    else:
        print(f"❌ {service_file} missing")
        return False
    
    # Check if routes file exists
    routes_file = 'src/backend/blueprints/translator_bot/translation_routes.py'
    if os.path.exists(routes_file):
        print(f"✅ {routes_file} exists")
    else:
        print(f"❌ {routes_file} missing")
        return False
    
    # Check if template file exists
    template_file = 'src/backend/blueprints/translator_bot/templates/translator_bot/translation_tool.html'
    if os.path.exists(template_file):
        print(f"✅ {template_file} exists")
    else:
        print(f"❌ {template_file} missing")
        return False
    
    print("✅ File structure tests passed!")
    return True

def main():
    """Main test function"""
    print("🧪 Testing DirectTranslationService Implementation")
    print("=" * 50)
    
    # Test file structure
    if not test_file_structure():
        return
    
    # Test service functionality
    test_translation_service()
    
    print("\n" + "=" * 50)
    print("🎉 Testing completed!")
    print("\nNext steps:")
    print("1. Install required dependencies (flask, pandas, openpyxl, requests)")
    print("2. Configure Azure OpenAI API keys in config files")
    print("3. Start the Flask application")
    print("4. Test the translation functionality through the web interface")

if __name__ == "__main__":
    main()
