import json
import pandas as pd
from openai import AzureOpenAI
from openpyxl import load_workbook
import time
import os
from typing import List, Dict, <PERSON><PERSON>
from flask import current_app
from config.config import EproExcelLaConfig
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DirectTranslationService:
    """
    Direct translation service based on excel_gpt_translator logic.
    Handles Excel file translation using direct API calls to Azure OpenAI.
    """
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.config = EproExcelLaConfig()
        self.upload_dir = os.path.join(
            os.path.dirname(__file__), '..', '..', '..', 'agents', 'eproexcella_agent', 'upload'
        )
        self.excel_path = os.path.join(self.upload_dir, f"data_{user_id}.xlsx")
        
        # Initialize Azure OpenAI client using .env variables
        self.client = AzureOpenAI(
            api_key=os.getenv("AZURE_API_TOKEN"),
            api_version=os.getenv("AZURE_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_ENDPOINT"),
        )
        
        # Store deployment name for later use
        self.deployment_name = os.getenv("AZURE_DEPLOYMENT_NAME")
        
    def read_excel_column_to_json(self, column_name: str, max_rows: int = 200) -> Tuple[List[Dict], str]:
        """
        Read a column from an Excel file and transform it into JSON arrays with pagination.
        
        Args:
            column_name (str): Column letter or name to read
            max_rows (int): Maximum number of rows per JSON array (pagination)
            
        Returns:
            Tuple[List[Dict], str]: List of JSON objects and header value
        """
        # Load the Excel file
        df = pd.read_excel(self.excel_path)
        
        # Convert column name to proper format if it's a letter
        if len(column_name) == 1 and column_name.isalpha():
            try:
                column_name = df.columns[ord(column_name.upper()) - ord('A')]
            except IndexError:
                raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Check if the column exists
        if column_name not in df.columns:
            raise ValueError(f"Column {column_name} not found in the Excel file")
        
        # Get the header
        header = column_name
        
        # Get the column data (excluding header)
        column_data = df[column_name].fillna("").astype(str)
        
        # Create JSON arrays with pagination
        json_arrays = []
        for i in range(0, len(column_data), max_rows):
            chunk = column_data.iloc[i:i+max_rows]
            json_obj = {
                "column": column_name,
                "content": {str(j+1+i): value for j, value in enumerate(chunk)}
            }
            json_arrays.append(json_obj)
        
        return json_arrays, header
    
    def submit_to_gpt(self, json_obj: Dict, prompt: str) -> str:
        """
        Submit a JSON object to Azure GPT with a translation prompt.
        
        Args:
            json_obj (Dict): JSON object to submit
            prompt (str): Translation prompt to use
            
        Returns:
            str: Response from GPT
        """
        start_call = time.time()
        current_app.logger.info(f"Sending request to OpenAI with payload of {len(json.dumps(json_obj))} characters...")
        
        try:
            # Prepare the messages
            messages = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": json.dumps(json_obj)}
            ]
            
            # Make the API request using Azure OpenAI client
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                temperature=0,
                top_p=0.1,
            )
            
            duration = time.time() - start_call
            current_app.logger.info(f"Response received from OpenAI in {duration:.2f} seconds.")
            
            return response.choices[0].message.content
            
        except Exception as e:
            duration = time.time() - start_call
            error_message = f"Azure OpenAI API request failed after {duration:.2f} seconds: {str(e)}"
            current_app.logger.error(error_message)
            current_app.logger.error(f"Model: {self.deployment_name}")
            current_app.logger.error(f"Endpoint: {os.getenv('AZURE_ENDPOINT')}")
            raise Exception(error_message)
    
    def write_results_to_excel(self, results: List[str], output_column: str, header: str):
        """
        Write the results to a new column in the Excel file.
        
        Args:
            results (List[str]): List of results to write
            output_column (str): Column letter or name to write to
            header (str): Header value for the output column
        """
        # Load the workbook and get the active sheet
        workbook = load_workbook(self.excel_path)
        sheet = workbook.active
        
        # Determine the column index
        if len(output_column) == 1 and output_column.isalpha():
            col_idx = ord(output_column.upper()) - ord('A') + 1
        else:
            # If it's not a single letter, we'll need to find its index
            df = pd.read_excel(self.excel_path)
            if output_column in df.columns:
                col_idx = df.columns.get_loc(output_column) + 1
            else:
                # If the column doesn't exist, create it
                col_idx = len(df.columns) + 1
        
        # Write the header first
        header_text = f"{header}_translated" if header else "Translated"
        sheet.cell(row=1, column=col_idx, value=header_text)
        
        # Write the results to the Excel file, starting from row 2 (after header)
        for i, result in enumerate(results):
            try:
                # Parse the result as JSON to extract the values
                result_json = json.loads(result)
                
                # Write each value to the corresponding row (add 1 for header row)
                for row_idx, value in result_json.items():
                    sheet.cell(row=int(row_idx)+1, column=col_idx, value=value)
            except json.JSONDecodeError:
                # If the result is not valid JSON, write it as is
                sheet.cell(row=i+2, column=col_idx, value=result)  # +2 to account for header and 0-indexing
        
        # Save the workbook
        workbook.save(self.excel_path)

    def get_default_prompt(self, target_language: str) -> str:
        """
        Get the default translation prompt for the specified target language.

        Args:
            target_language (str): Target language for translation

        Returns:
            str: Default translation prompt
        """
        return f"""
        You are a professional translator specializing in technical documentation for industrial equipment.
        You will be provided with product descriptions for professional laundry machines and spare parts, in particular for TOSEI products.
        Translate each description into clear, accurate {target_language} while preserving technical terminology.
        Double check the meaning of the single words and phrases to ensure accuracy with the context.

        IMPORTANT: You must format your response as a valid JSON object where the keys are the row numbers and the values are the translated {target_language} text. For example:
        {{"1": "30kg Industrial Washing Machine", "2": "Stainless Steel Dryer with Steam Function", "3": "Commercial Ironing System"}}

        Do not include any explanations or notes outside of this JSON structure.
        """

    def translate_column(self, column_name: str, target_language: str, max_rows: int = 200) -> Dict:
        """
        Translate a specific column in the Excel file.

        Args:
            column_name (str): Column name or letter to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls

        Returns:
            Dict: Translation result with success status and details
        """
        start_script = time.time()

        try:
            # Read the Excel column to JSON
            json_arrays, original_header = self.read_excel_column_to_json(column_name, max_rows)

            # Get the translation prompt
            prompt = self.get_default_prompt(target_language)

            # Submit each JSON array to GPT and collect the responses
            responses = []
            for idx, json_obj in enumerate(json_arrays):
                current_app.logger.info(f"Processing batch {idx+1}/{len(json_arrays)}")
                response = self.submit_to_gpt(json_obj, prompt)
                responses.append(response)

            # Determine output column (next available column)
            df = pd.read_excel(self.excel_path)
            output_column = chr(ord('A') + len(df.columns))  # Next column letter

            # Write the results to the Excel file
            self.write_results_to_excel(responses, output_column, original_header)

            total_duration = time.time() - start_script
            current_app.logger.info(f"Processed {len(json_arrays)} JSON arrays and wrote results to column {output_column}")
            current_app.logger.info(f"Script completed in {total_duration:.2f} seconds.")

            return {
                'success': True,
                'message': f'Translation completed successfully',
                'batches_processed': len(json_arrays),
                'output_column': output_column,
                'original_column': column_name,
                'target_language': target_language,
                'duration': total_duration
            }

        except Exception as e:
            current_app.logger.error(f"Translation error: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def translate_multiple_columns(self, column_names: List[str], target_language: str, max_rows: int = 200) -> Dict:
        """
        Translate multiple columns in the Excel file.

        Args:
            column_names (List[str]): List of column names or letters to translate
            target_language (str): Target language for translation
            max_rows (int): Maximum rows per batch for API calls

        Returns:
            Dict: Translation result with success status and details
        """
        start_script = time.time()
        results = []

        try:
            for column_name in column_names:
                current_app.logger.info(f"Translating column: {column_name}")
                result = self.translate_column(column_name, target_language, max_rows)
                results.append({
                    'column': column_name,
                    'result': result
                })

                if not result['success']:
                    current_app.logger.error(f"Failed to translate column {column_name}: {result.get('error')}")

            total_duration = time.time() - start_script
            successful_translations = [r for r in results if r['result']['success']]

            return {
                'success': len(successful_translations) > 0,
                'message': f'Translated {len(successful_translations)}/{len(column_names)} columns successfully',
                'results': results,
                'target_language': target_language,
                'total_duration': total_duration
            }

        except Exception as e:
            current_app.logger.error(f"Multiple column translation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': results
            }

    def get_excel_columns(self) -> List[str]:
        """
        Get the list of columns in the Excel file.

        Returns:
            List[str]: List of column names
        """
        try:
            df = pd.read_excel(self.excel_path)
            return df.columns.tolist()
        except Exception as e:
            current_app.logger.error(f"Error reading Excel columns: {e}")
            return []

    def preview_translation(self, column_name: str, target_language: str, preview_rows: int = 10) -> Dict:
        """
        Preview translation for a specific column (first few rows only).

        Args:
            column_name (str): Column name or letter to preview
            target_language (str): Target language for translation
            preview_rows (int): Number of rows to preview

        Returns:
            Dict: Preview result with translated sample
        """
        try:
            # Read only the first few rows for preview
            json_arrays, original_header = self.read_excel_column_to_json(column_name, preview_rows)

            if not json_arrays:
                return {'success': False, 'error': 'No data to preview'}

            # Get the translation prompt
            prompt = self.get_default_prompt(target_language)

            # Translate only the first batch for preview
            response = self.submit_to_gpt(json_arrays[0], prompt)

            # Parse the response to show preview
            try:
                result_json = json.loads(response)
                preview_data = []

                # Get original data for comparison
                df = pd.read_excel(self.excel_path)
                original_data = df[original_header].fillna("").astype(str)

                for row_idx, translated_value in result_json.items():
                    original_value = original_data.iloc[int(row_idx)-1] if int(row_idx)-1 < len(original_data) else ""
                    preview_data.append({
                        'row': int(row_idx),
                        'original': original_value,
                        'translated': translated_value
                    })

                return {
                    'success': True,
                    'preview_data': preview_data,
                    'column': column_name,
                    'target_language': target_language,
                    'total_rows_in_file': len(df)
                }

            except json.JSONDecodeError:
                return {
                    'success': False,
                    'error': 'Invalid response format from translation service'
                }

        except Exception as e:
            current_app.logger.error(f"Preview error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
